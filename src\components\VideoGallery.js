import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing } from '../constants';
import { PlaceholderImages } from '../utils/placeholderImages';

const { width: screenWidth } = Dimensions.get('window');
const VIDEO_CARD_WIDTH = screenWidth * 0.7;
const VIDEO_CARD_HEIGHT = 200;

// Sample video data
const videoData = [
  {
    id: 1,
    title: 'Safety Training - Keselamatan Kerja',
    duration: '15:30',
    thumbnail: PlaceholderImages.videos.safetyTraining,
    category: 'Training',
    uploadDate: '2024-01-15',
    views: 1250,
  },
  {
    id: 2,
    title: 'Company Update - Q1 2024',
    duration: '8:45',
    thumbnail: PlaceholderImages.videos.companyUpdate,
    category: 'Update',
    uploadDate: '2024-01-10',
    views: 890,
  },
  {
    id: 3,
    title: 'New Employee Orientation',
    duration: '22:15',
    thumbnail: PlaceholderImages.videos.orientation,
    category: 'Orientation',
    uploadDate: '2024-01-08',
    views: 567,
  },
  {
    id: 4,
    title: 'Production Excellence Workshop',
    duration: '18:20',
    thumbnail: PlaceholderImages.videos.workshop,
    category: 'Workshop',
    uploadDate: '2024-01-05',
    views: 432,
  },
];

const VideoGallery = () => {
  const flatListRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleVideoPress = (video) => {
    Alert.alert(
      video.title,
      `Durasi: ${video.duration}\nKategori: ${video.category}\nDilihat: ${video.views} kali`,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Putar Video',
          onPress: () => {
            console.log('Play video:', video.title);
            // Video player logic would go here
          },
        },
      ]
    );
  };

  const handleViewAll = () => {
    console.log('View all videos');
    // Navigate to full video gallery
  };

  const formatViews = (views) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k`;
    }
    return views.toString();
  };

  const scrollToNext = () => {
    if (currentIndex < videoData.length - 1) {
      const nextIndex = currentIndex + 1;
      flatListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    }
  };

  const scrollToPrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      flatListRef.current?.scrollToIndex({
        index: prevIndex,
        animated: true,
      });
      setCurrentIndex(prevIndex);
    }
  };

  const onScroll = (event) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / (VIDEO_CARD_WIDTH + Spacing.margin.md));
    setCurrentIndex(index);
  };

  const renderVideoItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.videoCard}
        onPress={() => handleVideoPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.thumbnailContainer}>
          <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} resizeMode="cover" />
          <View style={styles.playButton}>
            <MaterialIcons
              name="play-arrow"
              size={32}
              color={Colors.onPrimary}
            />
          </View>
          <View style={styles.durationBadge}>
            <Text style={styles.durationText}>{item.duration}</Text>
          </View>
        </View>
        
        <View style={styles.videoInfo}>
          <Text style={styles.videoTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.videoMeta}>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </View>
            <View style={styles.metaInfo}>
              <MaterialIcons
                name="visibility"
                size={14}
                color={Colors.onSurfaceVariant}
              />
              <Text style={styles.viewsText}>{formatViews(item.views)}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>Featured Videos</Text>
        <Text style={styles.sectionSubtitle}>
          Video pelatihan dan update terbaru
        </Text>
      </View>
      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>
        <Text style={styles.viewAllText}>Lihat Semua</Text>
        <MaterialIcons
          name="arrow-forward"
          size={16}
          color={Colors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderNavigationControls = () => (
    <View style={styles.navigationContainer}>
      {/* Navigation Buttons */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity
          style={[styles.navButton, currentIndex === 0 && styles.navButtonDisabled]}
          onPress={scrollToPrevious}
          disabled={currentIndex === 0}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name="chevron-left"
            size={24}
            color={currentIndex === 0 ? Colors.onSurfaceVariant : Colors.primary}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, currentIndex === videoData.length - 1 && styles.navButtonDisabled]}
          onPress={scrollToNext}
          disabled={currentIndex === videoData.length - 1}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name="chevron-right"
            size={24}
            color={currentIndex === videoData.length - 1 ? Colors.onSurfaceVariant : Colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Pagination Dots */}
      <View style={styles.paginationContainer}>
        {videoData.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.paginationDotActive
            ]}
            onPress={() => {
              flatListRef.current?.scrollToIndex({
                index,
                animated: true,
              });
              setCurrentIndex(index);
            }}
          />
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      {renderNavigationControls()}

      <FlatList
        ref={flatListRef}
        data={videoData}
        renderItem={renderVideoItem}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        snapToInterval={VIDEO_CARD_WIDTH + Spacing.margin.md}
        decelerationRate="fast"
        onScroll={onScroll}
        scrollEventThrottle={16}
        onScrollToIndexFailed={(info) => {
          // Handle scroll to index failure
          console.log('Scroll to index failed:', info);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: Spacing.padding.sm,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: Spacing.padding.md,
    marginBottom: Spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    ...Typography.body2,
    color: Colors.primary,
    marginRight: Spacing.xs,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: Spacing.padding.md,
  },
  videoCard: {
    width: VIDEO_CARD_WIDTH,
    marginRight: Spacing.margin.md,
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    overflow: 'hidden',
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbnailContainer: {
    position: 'relative',
    height: VIDEO_CARD_HEIGHT * 0.6,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: Spacing.xs,
    right: Spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  durationText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 10,
  },
  videoInfo: {
    padding: Spacing.padding.md,
  },
  videoTitle: {
    ...Typography.subtitle2,
    color: Colors.onSurface,
    marginBottom: Spacing.sm,
  },
  videoMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryBadge: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  categoryText: {
    ...Typography.caption,
    color: Colors.primary,
    fontSize: 10,
    fontWeight: '500',
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewsText: {
    ...Typography.caption,
    color: Colors.onSurfaceVariant,
    marginLeft: Spacing.xs / 2,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.padding.md,
    marginBottom: Spacing.sm,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: Spacing.xs,
    borderWidth: 1,
    borderColor: Colors.outline,
    boxShadow: `0 2px 4px ${Colors.shadow}`,
    elevation: 2,
  },
  navButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    borderColor: Colors.outlineVariant,
    boxShadow: 'none',
    elevation: 0,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.outlineVariant,
    marginHorizontal: 3,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 20,
    borderRadius: 4,
  },
});

export default VideoGallery;
