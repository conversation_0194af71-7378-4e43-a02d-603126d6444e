import React, { useRef, useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  PanGestureHandler,
  GestureHandlerRootView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing } from '../constants';
import { PlaceholderImages } from '../utils/placeholderImages';

const { width: screenWidth } = Dimensions.get('window');
const VIDEO_CARD_WIDTH = screenWidth * 0.7;
const VIDEO_CARD_HEIGHT = 200;

// Sample video data
const videoData = [
  {
    id: 1,
    title: 'Safety Training - Keselamatan Kerja',
    duration: '15:30',
    thumbnail: PlaceholderImages.videos.safetyTraining,
    category: 'Training',
    uploadDate: '2024-01-15',
    views: 1250,
  },
  {
    id: 2,
    title: 'Company Update - Q1 2024',
    duration: '8:45',
    thumbnail: PlaceholderImages.videos.companyUpdate,
    category: 'Update',
    uploadDate: '2024-01-10',
    views: 890,
  },
  {
    id: 3,
    title: 'New Employee Orientation',
    duration: '22:15',
    thumbnail: PlaceholderImages.videos.orientation,
    category: 'Orientation',
    uploadDate: '2024-01-08',
    views: 567,
  },
  {
    id: 4,
    title: 'Production Excellence Workshop',
    duration: '18:20',
    thumbnail: PlaceholderImages.videos.workshop,
    category: 'Workshop',
    uploadDate: '2024-01-05',
    views: 432,
  },
];

const VideoGallery = () => {
  const flatListRef = useRef(null);
  const autoScrollTimer = useRef(null);
  const userInteractionTimer = useRef(null);
  const lastScrollTime = useRef(0);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  // Configuration constants
  const AUTO_SCROLL_INTERVAL = 4500; // 4.5 seconds
  const USER_INTERACTION_PAUSE = 3000; // 3 seconds
  const SWIPE_THRESHOLD = 50; // 50px minimum swipe distance
  const INFINITE_LOOP = true; // Enable infinite loop

  const handleVideoPress = (video) => {
    Alert.alert(
      video.title,
      `Durasi: ${video.duration}\nKategori: ${video.category}\nDilihat: ${video.views} kali`,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Putar Video',
          onPress: () => {
            console.log('Play video:', video.title);
            // Video player logic would go here
          },
        },
      ]
    );
  };

  const handleViewAll = () => {
    console.log('View all videos');
    // Navigate to full video gallery
  };

  const formatViews = (views) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k`;
    }
    return views.toString();
  };

  // Auto-scroll functionality
  const startAutoScroll = useCallback(() => {
    if (!isAutoScrollEnabled || isUserInteracting || isDragging || !flatListRef.current) return;

    clearTimeout(autoScrollTimer.current);
    autoScrollTimer.current = setTimeout(() => {
      if (!isUserInteracting && !isDragging && isAutoScrollEnabled && flatListRef.current) {
        let nextIndex;
        if (INFINITE_LOOP) {
          nextIndex = currentIndex >= videoData.length - 1 ? 0 : currentIndex + 1;
        } else {
          nextIndex = Math.min(currentIndex + 1, videoData.length - 1);
        }

        if (nextIndex !== currentIndex && nextIndex >= 0 && nextIndex < videoData.length) {
          try {
            scrollToIndex(nextIndex, true);
          } catch (error) {
            console.log('Auto-scroll failed:', error);
            // Fallback to offset scroll
            const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
            const offset = nextIndex * cardWidth;
            flatListRef.current?.scrollToOffset({
              offset,
              animated: true
            });
            setCurrentIndex(nextIndex);
          }
        }
      }
    }, AUTO_SCROLL_INTERVAL);
  }, [currentIndex, isAutoScrollEnabled, isUserInteracting, isDragging, scrollToIndex]);

  const pauseAutoScroll = useCallback(() => {
    setIsUserInteracting(true);
    clearTimeout(autoScrollTimer.current);
    clearTimeout(userInteractionTimer.current);

    userInteractionTimer.current = setTimeout(() => {
      setIsUserInteracting(false);
    }, USER_INTERACTION_PAUSE);
  }, []);

  const scrollToIndex = useCallback((index, animated = true) => {
    if (flatListRef.current && index >= 0 && index < videoData.length) {
      try {
        flatListRef.current.scrollToIndex({
          index,
          animated,
          viewPosition: 0, // Start position instead of center
        });
        setCurrentIndex(index);
      } catch (error) {
        console.log('ScrollToIndex failed, using scrollToOffset fallback:', error);
        // Fallback to scrollToOffset
        const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
        const offset = index * cardWidth;
        flatListRef.current.scrollToOffset({
          offset,
          animated
        });
        setCurrentIndex(index);
      }
    }
  }, []);

  // Auto-scroll effect
  useEffect(() => {
    if (isLayoutReady && isAutoScrollEnabled && !isUserInteracting && !isDragging) {
      startAutoScroll();
    }

    return () => {
      clearTimeout(autoScrollTimer.current);
      clearTimeout(userInteractionTimer.current);
    };
  }, [startAutoScroll, isAutoScrollEnabled, isUserInteracting, isDragging, isLayoutReady]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimeout(autoScrollTimer.current);
      clearTimeout(userInteractionTimer.current);
    };
  }, []);

  const scrollToNext = useCallback(() => {
    pauseAutoScroll();
    let nextIndex;

    if (INFINITE_LOOP) {
      nextIndex = currentIndex >= videoData.length - 1 ? 0 : currentIndex + 1;
    } else {
      nextIndex = Math.min(currentIndex + 1, videoData.length - 1);
    }

    if (nextIndex !== currentIndex) {
      scrollToIndex(nextIndex, true);
    }
  }, [currentIndex, pauseAutoScroll, scrollToIndex]);

  const scrollToPrevious = useCallback(() => {
    pauseAutoScroll();
    let prevIndex;

    if (INFINITE_LOOP) {
      prevIndex = currentIndex <= 0 ? videoData.length - 1 : currentIndex - 1;
    } else {
      prevIndex = Math.max(currentIndex - 1, 0);
    }

    if (prevIndex !== currentIndex) {
      scrollToIndex(prevIndex, true);
    }
  }, [currentIndex, pauseAutoScroll, scrollToIndex]);

  // Enhanced scroll handler with momentum and snap detection
  const onScroll = useCallback((event) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
    const index = Math.round(contentOffsetX / cardWidth);

    // Update current index if it changed
    if (index !== currentIndex && index >= 0 && index < videoData.length) {
      setCurrentIndex(index);
    }

    // Track scroll time for momentum detection
    lastScrollTime.current = Date.now();
  }, [currentIndex]);

  // Handle scroll begin (user starts touching)
  const onScrollBeginDrag = useCallback(() => {
    setIsDragging(true);
    pauseAutoScroll();
  }, [pauseAutoScroll]);

  // Handle scroll end (user releases touch)
  const onScrollEndDrag = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle momentum scroll end (final position reached)
  const onMomentumScrollEnd = useCallback((event) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
    const index = Math.round(contentOffsetX / cardWidth);

    // Ensure we're at the correct index
    if (index !== currentIndex && index >= 0 && index < videoData.length) {
      setCurrentIndex(index);
    }

    setIsDragging(false);
  }, [currentIndex]);

  const renderVideoItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.videoCard}
        onPress={() => handleVideoPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.thumbnailContainer}>
          <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} resizeMode="cover" />
          <View style={styles.playButton}>
            <MaterialIcons
              name="play-arrow"
              size={32}
              color={Colors.onPrimary}
            />
          </View>
          <View style={styles.durationBadge}>
            <Text style={styles.durationText}>{item.duration}</Text>
          </View>
        </View>
        
        <View style={styles.videoInfo}>
          <Text style={styles.videoTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.videoMeta}>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </View>
            <View style={styles.metaInfo}>
              <MaterialIcons
                name="visibility"
                size={14}
                color={Colors.onSurfaceVariant}
              />
              <Text style={styles.viewsText}>{formatViews(item.views)}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>Featured Videos</Text>
        <Text style={styles.sectionSubtitle}>
          Video pelatihan dan update terbaru
        </Text>
      </View>
      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>
        <Text style={styles.viewAllText}>Lihat Semua</Text>
        <MaterialIcons
          name="arrow-forward"
          size={16}
          color={Colors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderNavigationControls = () => {
    const isPrevDisabled = !INFINITE_LOOP && currentIndex === 0;
    const isNextDisabled = !INFINITE_LOOP && currentIndex === videoData.length - 1;

    return (
      <View style={styles.navigationContainer}>
        {/* Pagination Dots - Center */}
        <View style={styles.paginationContainer}>
          {videoData.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive
              ]}
              onPress={() => {
                pauseAutoScroll();
                scrollToIndex(index, true);
              }}
              activeOpacity={0.7}
            />
          ))}
        </View>

        {/* Auto-scroll indicator */}
        <View style={styles.autoScrollIndicator}>
          <TouchableOpacity
            style={[
              styles.autoScrollButton,
              isAutoScrollEnabled && styles.autoScrollButtonActive
            ]}
            onPress={() => {
              setIsAutoScrollEnabled(!isAutoScrollEnabled);
              if (!isAutoScrollEnabled) {
                setIsUserInteracting(false);
              }
            }}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name={isAutoScrollEnabled ? "pause" : "play-arrow"}
              size={16}
              color={isAutoScrollEnabled ? Colors.primary : Colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        </View>

        {/* Navigation Buttons - Right Side */}
        <View style={styles.navigationButtons}>
          <TouchableOpacity
            style={[styles.navButton, isPrevDisabled && styles.navButtonDisabled]}
            onPress={scrollToPrevious}
            disabled={isPrevDisabled}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="chevron-left"
              size={20}
              color={isPrevDisabled ? Colors.onSurfaceVariant : Colors.primary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, isNextDisabled && styles.navButtonDisabled]}
            onPress={scrollToNext}
            disabled={isNextDisabled}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="chevron-right"
              size={20}
              color={isNextDisabled ? Colors.onSurfaceVariant : Colors.primary}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderHeader()}

      <View style={styles.galleryContainer}>
        <FlatList
          ref={flatListRef}
          data={videoData}
          renderItem={renderVideoItem}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}

          // Touch-based scrolling configuration
          snapToInterval={VIDEO_CARD_WIDTH + Spacing.margin.md}
          snapToAlignment="start"
          decelerationRate="fast"

          // Scroll event handlers
          onScroll={onScroll}
          scrollEventThrottle={16} // 60fps performance
          onScrollBeginDrag={onScrollBeginDrag}
          onScrollEndDrag={onScrollEndDrag}
          onMomentumScrollEnd={onMomentumScrollEnd}

          // Touch interaction settings
          scrollEnabled={true}
          bounces={false}
          pagingEnabled={false} // Use snapToInterval instead

          // Performance optimizations
          removeClippedSubviews={false} // Disable to prevent scroll issues
          maxToRenderPerBatch={videoData.length} // Render all items
          windowSize={10}
          initialNumToRender={videoData.length} // Render all initially
          getItemLayout={(data, index) => ({
            length: VIDEO_CARD_WIDTH + Spacing.margin.md,
            offset: (VIDEO_CARD_WIDTH + Spacing.margin.md) * index,
            index,
          })}

          // Error handling
          onScrollToIndexFailed={(info) => {
            console.log('Scroll to index failed:', info);
            // Wait for layout then retry
            setTimeout(() => {
              if (flatListRef.current) {
                const offset = info.index * (VIDEO_CARD_WIDTH + Spacing.margin.md);
                flatListRef.current.scrollToOffset({
                  offset,
                  animated: true
                });
                setCurrentIndex(info.index);
              }
            }, 100);
          }}

          // Layout handling
          onLayout={() => {
            if (!isLayoutReady) {
              setIsLayoutReady(true);
            }
          }}

          // Accessibility
          accessible={true}
          accessibilityLabel="Featured videos gallery"
          accessibilityHint="Swipe left or right to browse videos"
        />
      </View>

      {renderNavigationControls()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: Spacing.padding.sm,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: Spacing.padding.md,
    marginBottom: Spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    ...Typography.body2,
    color: Colors.primary,
    marginRight: Spacing.xs,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: Spacing.padding.md,
  },
  galleryContainer: {
    position: 'relative',
  },
  videoCard: {
    width: VIDEO_CARD_WIDTH,
    marginRight: Spacing.margin.md,
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    overflow: 'hidden',
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbnailContainer: {
    position: 'relative',
    height: VIDEO_CARD_HEIGHT * 0.6,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: Spacing.xs,
    right: Spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  durationText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 10,
  },
  videoInfo: {
    padding: Spacing.padding.md,
  },
  videoTitle: {
    ...Typography.subtitle2,
    color: Colors.onSurface,
    marginBottom: Spacing.sm,
  },
  videoMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryBadge: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  categoryText: {
    ...Typography.caption,
    color: Colors.primary,
    fontSize: 10,
    fontWeight: '500',
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewsText: {
    ...Typography.caption,
    color: Colors.onSurfaceVariant,
    marginLeft: Spacing.xs / 2,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.padding.md,
    paddingVertical: Spacing.sm,
    position: 'relative',
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.outlineVariant,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 16,
    borderRadius: 3,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    right: Spacing.padding.md,
  },
  navButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: Colors.outline,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
  },
  navButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    borderColor: Colors.outlineVariant,
    shadowOpacity: 0,
    elevation: 0,
  },
  autoScrollIndicator: {
    position: 'absolute',
    left: Spacing.padding.md,
    top: '50%',
    transform: [{ translateY: -14 }],
  },
  autoScrollButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.outlineVariant,
  },
  autoScrollButtonActive: {
    backgroundColor: Colors.surface,
    borderColor: Colors.primary,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
  },
});

export default VideoGallery;
