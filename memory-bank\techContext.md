# Tech Context - PSG-BMI Portal

**Last Updated:** July 15, 2025  
**Tech Lead:** Development Team  
**Review Cycle:** Per Major Version Update  

## Technology Stack

### Core Framework
- **React Native:** 0.72.10
- **Expo SDK:** 50.0.0
- **React:** 18.2.0
- **Node.js:** 18.0.0+ (development requirement)

### Development Tools
- **Package Manager:** npm (primary), yarn (alternative)
- **Bundler:** Metro Bundler (React Native default)
- **Web Bundler:** Webpack 5 (via Expo)
- **Transpiler:** Babel with Expo preset
- **Development Server:** Expo CLI

### UI & Design
- **Design System:** Material Design 3
- **Icons:** @expo/vector-icons (MaterialIcons)
- **Safe Area:** react-native-safe-area-context
- **Styling:** StyleSheet.create (React Native built-in)

### State Management
- **Local State:** React Hooks (useState, useEffect)
- **Shared Logic:** Custom Hooks
- **Future:** React Context API for global state

### Platform Support
- **iOS:** 13.0+ (iPhone, iPad)
- **Android:** API 21+ (Android 5.0+)
- **Web:** Modern browsers (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)

## Development Environment

### Required Software
```bash
# Core requirements
Node.js 18.0.0+
npm 8.0.0+
Git (latest)
Expo CLI (latest)

# Platform-specific (optional)
Android Studio (Android development)
Xcode (iOS development - macOS only)
```

### Project Setup
```bash
# Clone and install
git clone <repository-url>
cd psg-bmi-portal
npm install

# Start development server
npm start
# or
npx expo start

# Platform-specific starts
npm run android  # Android emulator
npm run ios      # iOS simulator  
npm run web      # Web browser
```

### Environment Configuration
```bash
# Development URLs
Web: http://localhost:19006
Mobile: exp://************:3000
Metro: http://localhost:8081

# Environment variables (if needed)
# Create .env file for sensitive data
# Reference in techContext.md, never commit secrets
```

### Network Configuration

#### Known Network Issues & Solutions
1. **QR Code Connection Timeout**
   - **Solution 1:** Manual URL entry in Expo Go
   - **Solution 2:** Mobile hotspot method
   - **Solution 3:** Alternative ports (4000, 5000)

2. **Web Compilation Errors**
   - **Crypto/Stream polyfills:** Pre-configured in webpack.config.js
   - **Cache issues:** Use `npx expo start --clear`

3. **Metro Bundler Issues**
   - **Port conflicts:** Configured to use port 8081
   - **Cache clearing:** `npm start -- --reset-cache`

## Project Structure

### File Organization
```
psg-bmi-portal/
├── App.js                 # Main app entry point
├── index.js               # Expo registration
├── package.json           # Dependencies & scripts
├── babel.config.js        # Babel configuration
├── metro.config.js        # Metro bundler config
├── webpack.config.js      # Web-specific config
├── src/
│   ├── components/        # Reusable UI components
│   ├── screens/           # Screen components
│   ├── hooks/             # Custom React hooks
│   ├── constants/         # App constants & themes
│   └── utils/             # Utility functions
├── assets/                # Images, fonts, etc.
├── scripts/               # Build & utility scripts
└── memory-bank/           # Documentation & context
```

### Configuration Files

#### package.json
```json
{
  "main": "index.js",
  "scripts": {
    "start": "expo start",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web"
  }
}
```

#### babel.config.js
```javascript
module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: ['react-native-reanimated/plugin'],
  };
};
```

#### metro.config.js
```javascript
const { getDefaultConfig } = require('expo/metro-config');
const config = getDefaultConfig(__dirname);
module.exports = config;
```

## Dependencies

### Core Dependencies
```json
{
  "expo": "~50.0.0",
  "react": "18.2.0",
  "react-native": "0.72.10",
  "react-native-safe-area-context": "4.7.4",
  "@expo/vector-icons": "^13.0.0"
}
```

### Development Dependencies
```json
{
  "@babel/core": "^7.20.0",
  "babel-preset-expo": "~10.0.0"
}
```

### Future Dependencies (Planned)
```json
{
  "@react-native-async-storage/async-storage": "^1.19.0",
  "expo-secure-store": "~12.5.0",
  "react-native-reanimated": "~3.6.0",
  "@react-navigation/native": "^6.1.0",
  "react-query": "^3.39.0"
}
```

## Build Configuration

### Development Build
- **Hot Reload:** Enabled for instant feedback
- **Source Maps:** Enabled for debugging
- **Minification:** Disabled for faster builds
- **Bundle Size:** ~2MB (development)

### Production Build (Future)
- **Minification:** Enabled for smaller bundle
- **Source Maps:** Disabled for security
- **Optimization:** Tree shaking and dead code elimination
- **Bundle Size:** Target <1MB

### Platform-Specific Builds

#### Web Build
```javascript
// webpack.config.js optimizations
module.exports = {
  resolve: {
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
    },
  },
};
```

#### Mobile Build
```javascript
// app.json configuration
{
  "expo": {
    "name": "PSG-BMI Portal",
    "slug": "psg-bmi-portal",
    "version": "1.0.0",
    "platforms": ["ios", "android", "web"]
  }
}
```

## Performance Configuration

### Metro Bundler Optimization
```javascript
// metro.config.js
module.exports = {
  transformer: {
    minifierConfig: {
      keep_fnames: true,
      mangle: {
        keep_fnames: true,
      },
    },
  },
};
```

### React Native Performance
- **Hardware Acceleration:** Enabled for animations
- **Image Optimization:** Automatic resizing and caching
- **Bundle Splitting:** Configured for web platform
- **Memory Management:** Proper cleanup in useEffect

## Security Configuration

### Data Protection
- **Sensitive Data:** Use Expo SecureStore (future)
- **API Keys:** Environment variables only
- **Local Storage:** AsyncStorage for non-sensitive data only
- **Network:** HTTPS only for production APIs

### Code Security
- **Input Validation:** All user inputs validated
- **XSS Prevention:** Proper data sanitization
- **Dependency Scanning:** Regular security audits
- **Code Obfuscation:** Production builds only

## Testing Configuration

### Testing Framework (Future)
```json
{
  "jest": "^29.0.0",
  "@testing-library/react-native": "^12.0.0",
  "detox": "^20.0.0"
}
```

### Test Types
- **Unit Tests:** Component and hook testing
- **Integration Tests:** Feature workflow testing
- **E2E Tests:** Full user journey testing
- **Performance Tests:** Load and stress testing

## Deployment Configuration

### Development Deployment
- **Expo Go:** Direct testing on devices
- **Web Hosting:** Local development server
- **Hot Updates:** Instant code updates

### Production Deployment (Future)
- **iOS:** App Store via Expo Build Service
- **Android:** Google Play Store via Expo Build Service
- **Web:** Static hosting (Netlify, Vercel)
- **OTA Updates:** Expo Updates service

## Monitoring & Analytics

### Development Monitoring
- **Console Logging:** Comprehensive debug information
- **Error Boundaries:** Graceful error handling
- **Performance Monitoring:** React DevTools

### Production Monitoring (Future)
- **Crash Reporting:** Sentry integration
- **Analytics:** Expo Analytics or custom solution
- **Performance:** Real User Monitoring (RUM)
- **Usage Tracking:** Feature adoption metrics

## Constraints & Limitations

### Technical Constraints
- **Expo Managed Workflow:** Limited to Expo-compatible packages
- **Bundle Size:** Web bundle size limitations
- **Platform APIs:** Limited to React Native and Expo APIs
- **Offline Support:** Limited without additional configuration

### Performance Constraints
- **Memory Usage:** Mobile device limitations
- **Network:** Optimized for 3G connections
- **Battery:** Minimal background processing
- **Storage:** Limited local storage capacity

### Development Constraints
- **Hot Reload:** Occasional restart required
- **Platform Testing:** Requires multiple devices/emulators
- **Debugging:** Limited native debugging capabilities
- **Build Time:** Slower builds for production

---

**This tech context provides the foundation for all technical decisions. Any changes to the technology stack should be carefully evaluated for impact on existing code and future maintainability.**
