# Solusi Koneksi Expo Go Android

## 🚨 Ma<PERSON>ah yang <PERSON><PERSON><PERSON><PERSON>
Error: `java.net.SocketTimeoutException: failed to connect to /************ (port 8081)`

## 🔧 Solusi yang Tersedia

### **Solusi 1: Gunakan Web Browser (<PERSON><PERSON> Mudah)**
1. Buka browser di HP Android Anda
2. Masukkan URL: `http://************:19006`
3. Jika tidak bisa, coba: `http://localhost:19006` (jika di PC yang sama)

### **Solusi 2: Manual URL di Expo Go**
1. Buka aplikasi Expo Go di Android
2. Pilih "Enter URL manually"
3. Masukkan: `exp://************:8081`
4. <PERSON><PERSON> gagal, coba: `exp://************:19000`

### **Solusi 3: Perbaiki Firewall Windows**
1. Buka Windows Defender Firewall
2. Klik "Allow an app or feature through Windows Defender Firewall"
3. <PERSON><PERSON> "Change Settings" → "Allow another app"
4. Tambahkan:
   - `Node.js`
   - `Expo CLI`
   - `Metro Bundler`

### **Solusi 4: Restart dengan IP yang Benar**
1. Buka Command Prompt
2. Ke<PERSON>k: `ipconfig`
3. Catat IP Address WiFi Anda
4. Restart Expo dengan IP tersebut

### **Solusi 5: Gunakan Localhost Tunneling**
```bash
# Install ngrok (jika belum ada)
npm install -g ngrok

# Jalankan tunnel
ngrok http 19006

# Gunakan URL ngrok di browser HP
```

### **Solusi 6: Development Build (Recommended)**
```bash
# Install expo dev client
npx expo install expo-dev-client

# Build untuk Android
npx expo run:android

# Atau build APK
eas build --platform android --profile development
```

## 🎯 Rekomendasi Urutan Solusi

1. **Coba Web Browser dulu** - Paling mudah dan cepat
2. **Manual URL di Expo Go** - Jika web browser tidak bisa
3. **Perbaiki Firewall** - Jika masih ada masalah koneksi
4. **Development Build** - Solusi permanen terbaik

## 📱 URL yang Bisa Dicoba

### Web Browser:
- `http://************:19006`
- `http://localhost:19006` (jika di PC yang sama)

### Expo Go Manual URL:
- `exp://************:8081`
- `exp://************:19000`
- `exp://localhost:8081` (jika di PC yang sama)

## ✅ Cara Verifikasi Berhasil

1. **Web Browser**: Halaman login PSG-BMI muncul
2. **Expo Go**: Aplikasi terbuka tanpa error
3. **Login**: Bisa login dengan `admin` / `password`
4. **Navigation**: Bottom navigation berfungsi normal

## 🔍 Troubleshooting Tambahan

### Jika masih error:
1. Pastikan HP dan PC di WiFi yang sama
2. Restart router WiFi
3. Disable antivirus sementara
4. Coba hotspot HP ke PC
5. Gunakan kabel USB debugging

### Cek koneksi:
```bash
# Ping dari HP ke PC
ping ************

# Cek port terbuka
telnet ************ 8081
```

## 📞 Bantuan Darurat

Jika semua solusi gagal, gunakan:
1. **Android Emulator** di PC
2. **Web Browser** di PC
3. **iOS Simulator** (jika ada Mac)

---

**Catatan**: Masalah ini umum terjadi karena firewall, network configuration, atau port blocking. Solusi web browser biasanya paling reliable.
