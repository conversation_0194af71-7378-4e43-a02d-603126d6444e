import React, { createContext, useContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import * as Crypto from 'expo-crypto';

// Create Auth Context
const AuthContext = createContext({});

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [biometricSupported, setBiometricSupported] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  // Storage keys
  const TOKEN_KEY = 'auth_token';
  const USER_KEY = 'auth_user';
  const BIOMETRIC_KEY = 'biometric_enabled';

  // Check biometric support on app start
  useEffect(() => {
    checkBiometricSupport();
    loadStoredAuth();
  }, []);

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricSupported(compatible && enrolled);
      
      if (compatible && enrolled) {
        const biometricPref = await SecureStore.getItemAsync(BIOMETRIC_KEY);
        setBiometricEnabled(biometricPref === 'true');
      }
    } catch (error) {
      console.log('Biometric check error:', error);
      setBiometricSupported(false);
    }
  };

  const loadStoredAuth = async () => {
    try {
      setIsLoading(true);
      const storedToken = await SecureStore.getItemAsync(TOKEN_KEY);
      const storedUser = await SecureStore.getItemAsync(USER_KEY);

      if (storedToken && storedUser) {
        setToken(storedToken);
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.log('Error loading stored auth:', error);
      await clearAuth();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setIsLoading(true);
      
      // Mock API call - replace with real API in production
      const response = await mockLoginAPI(credentials);
      
      if (response.success) {
        // Store token and user data securely
        await SecureStore.setItemAsync(TOKEN_KEY, response.token);
        await SecureStore.setItemAsync(USER_KEY, JSON.stringify(response.user));
        
        setToken(response.token);
        setUser(response.user);
        setIsAuthenticated(true);
        
        return { success: true, user: response.user };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.log('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithBiometric = async () => {
    try {
      if (!biometricSupported || !biometricEnabled) {
        throw new Error('Biometric authentication not available');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate with your biometric',
        fallbackLabel: 'Use password instead',
        cancelLabel: 'Cancel',
      });

      if (result.success) {
        // Load stored credentials and auto-login
        await loadStoredAuth();
        return { success: true };
      } else {
        throw new Error('Biometric authentication failed');
      }
    } catch (error) {
      console.log('Biometric login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Clear secure storage
      await SecureStore.deleteItemAsync(TOKEN_KEY);
      await SecureStore.deleteItemAsync(USER_KEY);
      
      // Reset state
      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
      
      return { success: true };
    } catch (error) {
      console.log('Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const clearAuth = async () => {
    try {
      await SecureStore.deleteItemAsync(TOKEN_KEY);
      await SecureStore.deleteItemAsync(USER_KEY);
      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.log('Clear auth error:', error);
    }
  };

  const enableBiometric = async (enable) => {
    try {
      if (!biometricSupported) {
        throw new Error('Biometric authentication not supported');
      }

      await SecureStore.setItemAsync(BIOMETRIC_KEY, enable.toString());
      setBiometricEnabled(enable);
      return { success: true };
    } catch (error) {
      console.log('Enable biometric error:', error);
      return { success: false, error: error.message };
    }
  };

  const refreshToken = async () => {
    try {
      if (!token) return { success: false, error: 'No token available' };
      
      // Mock token refresh - replace with real API
      const response = await mockRefreshTokenAPI(token);
      
      if (response.success) {
        await SecureStore.setItemAsync(TOKEN_KEY, response.token);
        setToken(response.token);
        return { success: true, token: response.token };
      } else {
        throw new Error(response.message || 'Token refresh failed');
      }
    } catch (error) {
      console.log('Token refresh error:', error);
      await clearAuth();
      return { success: false, error: error.message };
    }
  };

  // Mock API functions - replace with real API calls
  const mockLoginAPI = async (credentials) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock validation
    if (credentials.username === 'admin' && credentials.password === 'password') {
      const token = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        `${credentials.username}_${Date.now()}`
      );
      
      return {
        success: true,
        token,
        user: {
          id: '1',
          username: credentials.username,
          name: 'Administrator',
          email: '<EMAIL>',
          role: 'admin',
          department: 'IT',
          avatar: null,
        }
      };
    } else {
      return {
        success: false,
        message: 'Invalid username or password'
      };
    }
  };

  const mockRefreshTokenAPI = async (currentToken) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate new token
    const newToken = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      `refresh_${Date.now()}`
    );
    
    return {
      success: true,
      token: newToken
    };
  };

  const value = {
    // State
    user,
    token,
    isLoading,
    isAuthenticated,
    biometricSupported,
    biometricEnabled,
    
    // Methods
    login,
    loginWithBiometric,
    logout,
    enableBiometric,
    refreshToken,
    clearAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
