# System Patterns - PSG-BMI Portal

**Last Updated:** July 15, 2025  
**Architecture Owner:** Development Team  
**Review Cycle:** Per Major Feature  

## Architecture Overview

### High-Level System Design

```
┌─────────────────────────────────────────────────────────┐
│                    PSG-BMI Portal                       │
│                  (React Native App)                     │
├─────────────────────────────────────────────────────────┤
│  Presentation Layer                                     │
│  ├── Screens (HomeScreen)                              │
│  ├── Components (Header, Navigation, etc.)             │
│  └── UI Constants (Colors, Typography, Spacing)        │
├─────────────────────────────────────────────────────────┤
│  Business Logic Layer                                   │
│  ├── Custom Hooks (useBottomNavigation)                │
│  ├── State Management (React Context + Hooks)          │
│  └── Utilities (Helper functions)                      │
├─────────────────────────────────────────────────────────┤
│  Data Layer                                             │
│  ├── Mock Data (Current Phase)                         │
│  ├── API Integration (Future Phase)                    │
│  └── Local Storage (Future Phase)                      │
├─────────────────────────────────────────────────────────┤
│  Platform Layer                                         │
│  ├── Expo SDK 50                                       │
│  ├── React Native Core                                 │
│  └── Platform-Specific Optimizations                   │
└─────────────────────────────────────────────────────────┘
```

### Core Design Patterns

#### 1. Component Composition Pattern
**Purpose:** Build complex UIs from simple, reusable components

**Implementation:**
```javascript
// Base components
<Card>
  <Header />
  <Content />
  <Actions />
</Card>

// Composed components
<StatCard data={stats} onPress={handlePress} />
<MenuCard app={application} onPress={handleNavigation} />
```

**Benefits:**
- Reusability across different screens
- Consistent styling and behavior
- Easy testing and maintenance
- Clear separation of concerns

#### 2. Custom Hooks Pattern
**Purpose:** Encapsulate complex logic and state management

**Implementation:**
```javascript
// useBottomNavigation hook
const { getContentPadding, totalHeight } = useBottomNavigation();

// Future hooks
const { user, login, logout } = useAuth();
const { data, loading, error } = useAPI(endpoint);
```

**Benefits:**
- Logic reuse across components
- Cleaner component code
- Easier testing of business logic
- Consistent state management

#### 3. Configuration-Driven UI Pattern
**Purpose:** Define UI behavior through data structures

**Implementation:**
```javascript
// Navigation configuration
const navigationItems = [
  { id: 1, name: 'Home', icon: 'home', label: 'Beranda' },
  { id: 2, name: 'Activities', icon: 'assignment', label: 'Aktivitas' },
];

// Menu configuration
const menuApplications = [
  { id: 1, name: 'Attendance', icon: 'schedule', color: '#2196F3' },
  { id: 2, name: 'ATR Pribadi', icon: 'person', color: '#4CAF50' },
];
```

**Benefits:**
- Easy to modify without code changes
- Consistent data structure
- Simplified testing
- Future API integration ready

#### 4. Platform-Specific Optimization Pattern
**Purpose:** Provide optimal experience on each platform

**Implementation:**
```javascript
// Platform-specific styles
...Platform.select({
  web: {
    position: 'fixed',
    backdropFilter: 'blur(10px)',
  },
  default: {
    position: 'absolute',
  },
})
```

**Benefits:**
- Native feel on each platform
- Performance optimization
- Platform-specific features
- Consistent codebase

### Component Architecture

#### Component Hierarchy
```
App
├── SafeAreaProvider
    ├── ErrorBoundary
        ├── HomeScreen
        │   ├── Header
        │   └── ScrollView
        │       ├── Carousel
        │       ├── DashboardOverview
        │       ├── MenuGrid
        │       ├── VideoGallery
        │       └── RecentActivities
        └── BottomNavigation (Fixed Overlay)
```

#### Component Categories

**1. Layout Components**
- `SafeAreaView` - Handle device safe areas
- `ScrollView` - Scrollable content container
- `View` - Basic layout container
- `Card` - Elevated content container

**2. Navigation Components**
- `BottomNavigation` - Fixed bottom navigation
- `Header` - Top navigation and user info
- `MenuGrid` - Application shortcuts

**3. Content Components**
- `Carousel` - Auto-scrolling announcements
- `DashboardOverview` - Statistics display
- `VideoGallery` - Media content
- `RecentActivities` - Activity timeline

**4. UI Components**
- `Button` - Interactive elements
- `Text` - Typography with consistent styling
- `Icon` - Vector icons with theming

### State Management Patterns

#### Current State Architecture
```javascript
// Local component state
const [refreshing, setRefreshing] = useState(false);
const [currentIndex, setCurrentIndex] = useState(0);

// Custom hooks for shared logic
const { getContentPadding } = useBottomNavigation();

// Context for app-wide state (future)
const { user, theme } = useAppContext();
```

#### State Flow Patterns

**1. Unidirectional Data Flow**
- Props flow down from parent to child
- Events bubble up from child to parent
- State updates trigger re-renders

**2. Local State First**
- Component-specific state stays local
- Shared state promoted to custom hooks
- Global state only for truly global data

**3. Derived State Pattern**
- Calculate values from existing state
- Avoid storing computed values
- Use useMemo for expensive calculations

### Error Handling Patterns

#### Error Boundary Pattern
```javascript
<ErrorBoundary>
  <App />
</ErrorBoundary>
```

**Handles:**
- JavaScript errors in component tree
- Render errors and lifecycle errors
- Provides fallback UI
- Logs errors for debugging

#### Graceful Degradation Pattern
- Network errors show cached content
- Missing images show placeholders
- Failed API calls show retry options
- Offline mode with limited functionality

### Performance Patterns

#### 1. Lazy Loading Pattern
```javascript
// Future implementation
const LazyComponent = lazy(() => import('./HeavyComponent'));

<Suspense fallback={<Loading />}>
  <LazyComponent />
</Suspense>
```

#### 2. Memoization Pattern
```javascript
// Expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Component memoization
const MemoizedComponent = memo(Component);
```

#### 3. Virtual Scrolling Pattern
```javascript
// For large lists (future)
<FlatList
  data={largeDataSet}
  renderItem={renderItem}
  getItemLayout={getItemLayout}
  removeClippedSubviews={true}
/>
```

### Integration Patterns

#### API Integration Pattern (Future)
```javascript
// Service layer
class APIService {
  async get(endpoint) {
    const response = await fetch(`${BASE_URL}${endpoint}`);
    return response.json();
  }
}

// Hook integration
const useAPI = (endpoint) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    APIService.get(endpoint)
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [endpoint]);
  
  return { data, loading, error };
};
```

#### Authentication Pattern (Future)
```javascript
// Auth context
const AuthContext = createContext();

// Auth provider
const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  
  const login = async (credentials) => {
    const response = await authAPI.login(credentials);
    setUser(response.user);
    setToken(response.token);
    await SecureStore.setItemAsync('token', response.token);
  };
  
  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### Testing Patterns

#### Component Testing Pattern
```javascript
// Test structure
describe('Component', () => {
  it('renders correctly', () => {
    render(<Component />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
  
  it('handles user interaction', () => {
    const onPress = jest.fn();
    render(<Component onPress={onPress} />);
    fireEvent.press(screen.getByRole('button'));
    expect(onPress).toHaveBeenCalled();
  });
});
```

#### Hook Testing Pattern
```javascript
// Custom hook testing
const { result } = renderHook(() => useBottomNavigation());
expect(result.current.totalHeight).toBeGreaterThan(0);
```

### Security Patterns

#### Data Protection Pattern
- No sensitive data in AsyncStorage
- Use SecureStore for tokens
- Validate all user inputs
- Sanitize data before display

#### Network Security Pattern
- HTTPS only for API calls
- Certificate pinning (production)
- Request/response validation
- Timeout and retry logic

---

**These patterns ensure consistent, maintainable, and scalable code across the entire application. New features should follow these established patterns unless there's a compelling reason to deviate.**
