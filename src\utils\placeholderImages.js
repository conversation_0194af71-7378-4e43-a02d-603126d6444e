/**
 * Placeholder image utility for offline/local development
 * Generates colored placeholder images using data URIs
 */

// Generate a colored placeholder image as data URI
const generatePlaceholderImage = (width, height, backgroundColor, textColor, text) => {
  // Create SVG placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${backgroundColor}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" 
            fill="${textColor}" text-anchor="middle" dominant-baseline="middle">
        ${text}
      </text>
    </svg>
  `;
  
  // Convert to data URI
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Predefined placeholder images
export const PlaceholderImages = {
  // Carousel images
  carousel: {
    welcome: generatePlaceholderImage(300, 150, '#2196F3', '#FFFFFF', 'Welcome'),
    attendance: generatePlaceholderImage(300, 150, '#4CAF50', '#FFFFFF', 'Attendance'),
    safety: generatePlaceholderImage(300, 150, '#FF9800', '#FFFFFF', 'Safety'),
    achievement: generatePlaceholderImage(300, 150, '#9C27B0', '#FFFFFF', 'Achievement'),
  },
  
  // Video thumbnails
  videos: {
    safetyTraining: generatePlaceholderImage(300, 200, '#4CAF50', '#FFFFFF', 'Safety Training'),
    companyUpdate: generatePlaceholderImage(300, 200, '#2196F3', '#FFFFFF', 'Company Update'),
    orientation: generatePlaceholderImage(300, 200, '#9C27B0', '#FFFFFF', 'Orientation'),
    workshop: generatePlaceholderImage(300, 200, '#FF9800', '#FFFFFF', 'Workshop'),
  },
  
  // Profile avatars
  avatars: {
    default: generatePlaceholderImage(40, 40, '#2196F3', '#FFFFFF', 'JD'),
    admin: generatePlaceholderImage(40, 40, '#4CAF50', '#FFFFFF', 'AD'),
    user: generatePlaceholderImage(40, 40, '#FF9800', '#FFFFFF', 'US'),
  },
  
  // Menu icons (if needed)
  menu: {
    attendance: generatePlaceholderImage(60, 60, '#4CAF50', '#FFFFFF', 'ATT'),
    atr: generatePlaceholderImage(60, 60, '#2196F3', '#FFFFFF', 'ATR'),
    cnm: generatePlaceholderImage(60, 60, '#FF9800', '#FFFFFF', 'CNM'),
    points: generatePlaceholderImage(60, 60, '#9C27B0', '#FFFFFF', 'PTS'),
    payroll: generatePlaceholderImage(60, 60, '#F44336', '#FFFFFF', 'PAY'),
    elearning: generatePlaceholderImage(60, 60, '#00BCD4', '#FFFFFF', 'EDU'),
    helpdesk: generatePlaceholderImage(60, 60, '#795548', '#FFFFFF', 'HLP'),
    settings: generatePlaceholderImage(60, 60, '#607D8B', '#FFFFFF', 'SET'),
  }
};

// Fallback function for external images
export const getPlaceholderImage = (type, subtype = 'default') => {
  try {
    return PlaceholderImages[type]?.[subtype] || PlaceholderImages.avatars.default;
  } catch (error) {
    console.warn('Placeholder image not found:', type, subtype);
    return PlaceholderImages.avatars.default;
  }
};

// Helper function to create custom placeholder
export const createCustomPlaceholder = (width, height, color, text) => {
  return generatePlaceholderImage(width, height, color, '#FFFFFF', text);
};

export default PlaceholderImages;
