import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { Colors, Typography, Spacing, Animations } from '../constants';
import { PlaceholderImages } from '../utils/placeholderImages';

const Header = () => {
  const [notificationCount, setNotificationCount] = React.useState(3);
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const { user, logout } = useAuth();

  const handleNotificationPress = () => {
    // Animate notification icon
    Animated.sequence([
      Animated.spring(scaleValue, {
        toValue: 1.2,
        ...Animations.spring.bouncy,
        useNativeDriver: true,
      }),
      Animated.spring(scaleValue, {
        toValue: 1,
        ...Animations.spring.bouncy,
        useNativeDriver: true,
      }),
    ]).start();

    // Handle notification press logic here
    console.log('Notifications pressed');
  };

  const handleProfilePress = () => {
    Alert.alert(
      'Profile Menu',
      'Choose an action',
      [
        { text: 'View Profile', onPress: () => console.log('View Profile') },
        { text: 'Settings', onPress: () => console.log('Settings') },
        { text: 'Logout', onPress: handleLogout, style: 'destructive' },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            const result = await logout();
            if (!result.success) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        },
      ]
    );
  };

  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Selamat Pagi';
    if (hour < 15) return 'Selamat Siang';
    if (hour < 18) return 'Selamat Sore';
    return 'Selamat Malam';
  };

  return (
    <View style={styles.container}>
      {/* Background gradient effect */}
      <View style={styles.backgroundGradient} />
      
      <View style={styles.content}>
        {/* Left side - Profile */}
        <TouchableOpacity
          style={styles.profileSection}
          onPress={handleProfilePress}
          activeOpacity={0.8}
        >
          <View style={styles.avatarContainer}>
            <Image
              source={{
                uri: PlaceholderImages.avatars.admin
              }}
              style={styles.avatar}
            />
            <View style={styles.onlineIndicator} />
          </View>
          
          <View style={styles.greetingContainer}>
            <Text style={styles.greeting}>{getCurrentGreeting()}</Text>
            <Text style={styles.userName}>{user?.name || 'User'}</Text>
          </View>
        </TouchableOpacity>

        {/* Right side - Actions */}
        <View style={styles.actionsContainer}>
          {/* Search Button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => console.log('Search pressed')}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="search"
              size={Spacing.iconSize.md}
              color={Colors.onPrimary}
            />
          </TouchableOpacity>

          {/* Notification Button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleNotificationPress}
            activeOpacity={0.7}
          >
            <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
              <MaterialIcons
                name="notifications"
                size={Spacing.iconSize.md}
                color={Colors.onPrimary}
              />
              {notificationCount > 0 && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </Text>
                </View>
              )}
            </Animated.View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.primary,
    paddingTop: Spacing.sm,
    paddingBottom: Spacing.md,
    position: 'relative',
    overflow: 'hidden',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.primaryVariant,
    opacity: 0.1,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.padding.md,
    paddingVertical: Spacing.padding.sm,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: Spacing.md,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.onPrimary,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.success,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  greetingContainer: {
    flex: 1,
  },
  greeting: {
    ...Typography.caption,
    color: Colors.onPrimary,
    opacity: 0.9,
    marginBottom: 2,
  },
  userName: {
    ...Typography.subtitle1,
    color: Colors.onPrimary,
    fontWeight: '600',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: Colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  badgeText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 10,
    fontWeight: '600',
  },
});

export default Header;
