@echo off
echo ========================================
echo PSG-BMI Portal - Android Development Build
echo ========================================
echo.

echo [1/4] Checking prerequisites...
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Android Debug Bridge (adb) not found!
    echo Please install Android Studio and add adb to PATH
    pause
    exit /b 1
)

echo [2/4] Checking connected devices...
adb devices
echo.

echo [3/4] Building development client...
echo This will take several minutes...
npx expo run:android

echo.
echo [4/4] Build completed!
echo.
echo Instructions:
echo 1. The app should automatically install on your connected Android device
echo 2. Open the PSG-BMI Portal app (not Expo Go)
echo 3. The app will connect directly without network issues
echo.
pause
