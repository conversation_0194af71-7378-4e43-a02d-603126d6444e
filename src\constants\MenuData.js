// Menu data for the 8 main applications
import { PlaceholderImages } from '../utils/placeholderImages';
export const MenuData = [
  {
    id: 1,
    title: 'Attendance Recording',
    subtitle: '<PERSON>bsens<PERSON> & Kehadiran',
    icon: 'access-time',
    iconType: 'MaterialIcons',
    color: '#4CAF50',
    route: 'Attendance',
    description: 'Informasi absensi bulanan, status kehadiran real-time, pengajuan izin/cuti',
  },
  {
    id: 2,
    title: 'ATR Pribadi',
    subtitle: 'Pengajuan Kerja',
    icon: 'assignment',
    iconType: 'MaterialIcons',
    color: '#2196F3',
    route: 'ATR',
    description: 'Pengajuan kerja, dinas pribadi, status approval',
  },
  {
    id: 3,
    title: 'CNM',
    subtitle: 'Maintenance',
    icon: 'build',
    iconType: 'MaterialIcons',
    color: '#FF9800',
    route: 'CNM',
    description: 'Sistem maintenance, work orders, equipment status',
  },
  {
    id: 4,
    title: 'New Points',
    subtitle: 'Reward System',
    icon: 'star',
    iconType: 'MaterialIcons',
    color: '#9C27B0',
    route: 'Points',
    description: 'Reward points system, achievement tracking, redemption catalog',
  },
  {
    id: 5,
    title: 'SAP',
    subtitle: 'SHE & Greencard',
    icon: 'folder',
    iconType: 'MaterialIcons',
    color: '#607D8B',
    route: 'SAP',
    description: 'Modul SAP SHE, greencard system, IUT & OTT',
  },
  {
    id: 6,
    title: 'iPeak',
    subtitle: 'Performance',
    icon: 'trending-up',
    iconType: 'MaterialIcons',
    color: '#E91E63',
    route: 'iPeak',
    description: 'Performance tracking, penilaian karyawan, kompetensi development',
  },
  {
    id: 7,
    title: 'Production',
    subtitle: 'Data Produksi',
    icon: 'bar-chart',
    iconType: 'MaterialIcons',
    color: '#795548',
    route: 'Production',
    description: 'Data produksi harian, grafik & statistik, KPI monitoring',
  },
  {
    id: 8,
    title: 'View All',
    subtitle: 'Semua Aplikasi',
    icon: 'apps',
    iconType: 'MaterialIcons',
    color: '#424242',
    route: 'ViewAll',
    description: 'Semua aplikasi, search functionality, kategori filter',
  },
];

// Carousel data for featured content
export const CarouselData = [
  {
    id: 1,
    title: 'Selamat Datang di Portal PSG-BMI',
    subtitle: 'Akses semua layanan karyawan dalam satu aplikasi',
    image: PlaceholderImages.carousel.welcome,
    type: 'welcome',
  },
  {
    id: 2,
    title: 'Update Sistem Absensi',
    subtitle: 'Fitur baru untuk pencatatan kehadiran yang lebih akurat',
    image: PlaceholderImages.carousel.attendance,
    type: 'update',
  },
  {
    id: 3,
    title: 'Training Safety Bulan Ini',
    subtitle: 'Ikuti pelatihan keselamatan kerja wajib',
    image: PlaceholderImages.carousel.safety,
    type: 'training',
  },
  {
    id: 4,
    title: 'Pencapaian Produksi Q1',
    subtitle: 'Target produksi kuartal pertama tercapai 105%',
    image: PlaceholderImages.carousel.achievement,
    type: 'achievement',
  },
];

// Dashboard stats data
export const DashboardStats = [
  {
    id: 1,
    title: 'Check-in Hari Ini',
    value: '07:45',
    subtitle: 'Tepat waktu',
    icon: 'schedule',
    color: '#4CAF50',
    trend: 'positive',
  },
  {
    id: 2,
    title: 'Tingkat Kehadiran',
    value: '96%',
    subtitle: 'Bulan ini',
    icon: 'trending-up',
    color: '#2196F3',
    trend: 'positive',
  },
  {
    id: 3,
    title: 'Tugas Pending',
    value: '3',
    subtitle: 'Perlu diselesaikan',
    icon: 'assignment',
    color: '#FF9800',
    trend: 'neutral',
  },
  {
    id: 4,
    title: 'Points Reward',
    value: '1,250',
    subtitle: 'Total poin',
    icon: 'star',
    color: '#9C27B0',
    trend: 'positive',
  },
];
