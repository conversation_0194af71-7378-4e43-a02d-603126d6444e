import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getBottomNavHeight, BOTTOM_NAV_HEIGHT, FAB_ELEVATION } from '../components/BottomNavigation';

/**
 * Custom hook to get bottom navigation dimensions and utilities
 * @returns {Object} Object containing navigation height info and utilities
 */
export const useBottomNavigation = () => {
  const insets = useSafeAreaInsets();
  
  return {
    // Heights
    baseHeight: BOTTOM_NAV_HEIGHT,
    safeAreaBottom: insets.bottom,
    totalHeight: getBottomNavHeight(insets.bottom),
    fabElevation: FAB_ELEVATION,
    
    // Utility functions
    getContentPadding: (extraPadding = 0) => {
      return getBottomNavHeight(insets.bottom) + extraPadding;
    },
    
    // Style helpers
    getContentStyle: (extraPadding = 0) => ({
      paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding,
    }),
    
    getScrollContentStyle: (extraPadding = 0) => ({
      paddingBottom: getBottomNavHeight(insets.bottom) + extraPadding,
    }),
  };
};

export default useBottomNavigation;
