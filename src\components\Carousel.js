import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  Image,
  Animated,
} from 'react-native';
import { Colors, Typography, Spacing, Animations } from '../constants';
import { CarouselData } from '../constants/MenuData';

const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = screenWidth - (Spacing.padding.md * 2);
const CARD_HEIGHT = 180;
const AUTO_SCROLL_INTERVAL = 4000; // 4 seconds

const Carousel = () => {
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [isUserInteracting, setIsUserInteracting] = React.useState(false);
  const flatListRef = React.useRef(null);
  const autoScrollTimer = React.useRef(null);
  const scaleValues = React.useRef(
    CarouselData.map(() => new Animated.Value(1))
  ).current;

  // Auto-scroll functionality
  React.useEffect(() => {
    if (!isUserInteracting) {
      autoScrollTimer.current = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % CarouselData.length;
          flatListRef.current?.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          return nextIndex;
        });
      }, AUTO_SCROLL_INTERVAL);
    }

    return () => {
      if (autoScrollTimer.current) {
        clearInterval(autoScrollTimer.current);
      }
    };
  }, [isUserInteracting]);

  const handleScrollBegin = () => {
    setIsUserInteracting(true);
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }
  };

  const handleScrollEnd = () => {
    // Resume auto-scroll after 2 seconds of no interaction
    setTimeout(() => {
      setIsUserInteracting(false);
    }, 2000);
  };

  const handleMomentumScrollEnd = (event) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const index = Math.round(contentOffset.x / CARD_WIDTH);
    setCurrentIndex(index);
  };

  const handleCardPress = (item, index) => {
    // Animate the pressed card
    Animated.sequence([
      Animated.spring(scaleValues[index], {
        toValue: 0.95,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.spring(scaleValues[index], {
        toValue: 1,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
    ]).start();

    console.log('Carousel item pressed:', item.title);
  };

  const renderCarouselItem = ({ item, index }) => {
    return (
      <Animated.View
        style={[
          styles.cardContainer,
          { transform: [{ scale: scaleValues[index] }] },
        ]}
      >
        <TouchableOpacity
          style={styles.card}
          onPress={() => handleCardPress(item, index)}
          activeOpacity={0.9}
        >
          <Image source={{ uri: item.image }} style={styles.cardImage} resizeMode="cover" />
          <View style={styles.cardOverlay}>
            <View style={styles.cardContent}>
              <Text style={styles.cardTitle} numberOfLines={2}>
                {item.title}
              </Text>
              <Text style={styles.cardSubtitle} numberOfLines={2}>
                {item.subtitle}
              </Text>
            </View>
            <View style={[styles.typeIndicator, styles[`type_${item.type}`]]}>
              <Text style={styles.typeText}>{item.type.toUpperCase()}</Text>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderPagination = () => (
    <View style={styles.pagination}>
      {CarouselData.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            index === currentIndex && styles.paginationDotActive,
          ]}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={CarouselData}
        renderItem={renderCarouselItem}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScrollBeginDrag={handleScrollBegin}
        onScrollEndDrag={handleScrollEnd}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        contentContainerStyle={styles.flatListContent}
        snapToInterval={CARD_WIDTH}
        decelerationRate="fast"
        getItemLayout={(data, index) => ({
          length: CARD_WIDTH,
          offset: CARD_WIDTH * index,
          index,
        })}
      />
      {renderPagination()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.padding.md,
  },
  flatListContent: {
    paddingVertical: Spacing.padding.sm,
  },
  cardContainer: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    marginRight: 0,
  },
  card: {
    flex: 1,
    borderRadius: Spacing.borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: Colors.surface,
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: Spacing.padding.md,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    ...Typography.h3,
    color: Colors.onPrimary,
    marginBottom: Spacing.xs,
  },
  cardSubtitle: {
    ...Typography.body2,
    color: Colors.onPrimary,
    opacity: 0.9,
  },
  typeIndicator: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    paddingHorizontal: Spacing.xs,
    paddingVertical: Spacing.xs / 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  type_welcome: {
    backgroundColor: Colors.primary,
  },
  type_update: {
    backgroundColor: Colors.success,
  },
  type_training: {
    backgroundColor: Colors.warning,
  },
  type_achievement: {
    backgroundColor: Colors.info,
  },
  typeText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 10,
    fontWeight: '600',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.outlineVariant,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 24,
  },
});

export default Carousel;
